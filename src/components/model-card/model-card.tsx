import React from 'react';
import { useNavigate } from 'react-router-dom';
import type { ModelData } from '../../services/api';
import './model-card.css';

interface ModelCardProps {
  model: ModelData;
  className?: string;
}

export const ModelCard: React.FC<ModelCardProps> = ({ model, className = '' }) => {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate(`/render/${model.id}`);
  };

  return (
    <div className={`model-card ${className}`} onClick={handleClick}>
      <div className="model-card__content">
        <h3 className="model-card__title">{model.name}</h3>
      </div>
      <div className="model-card__image-container">
        {model.thumbnail ? (
          <img
            src={model.thumbnail}
            alt={model.name}
            className="model-card__image"
            onError={(e) => {
              // 如果图片加载失败，显示占位符
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              const placeholder = target.parentElement?.querySelector('.model-card__placeholder') as HTMLElement;
              if (placeholder) {
                placeholder.classList.remove('hidden');
              }
            }}
          />
        ) : null}
        <div className={`model-card__placeholder ${model.thumbnail ? 'hidden' : ''}`}>
          <div className="model-card__placeholder-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
              <polyline points="3.27,6.96 12,12.01 20.73,6.96"/>
              <line x1="12" y1="22.08" x2="12" y2="12"/>
            </svg>
          </div>
        </div>

      </div>
    </div>
  );
};